import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import aiService from "../../services/ai";
import oauthService from "../../services/oauth";
import { MessengerOAuthButton, InstagramOAuthButton } from "../../components/OAuthButton";
import { 
  MessageSquare, 
  Instagram, 
  MessageCircle, 
  Settings, 
  CircleCheck, 
  XCircle, 
  AlertCircle,
  ExternalLink,
  Power,
  RotateCcw
} from "lucide-react";

export default function SocialMediaIntegrations() {
  const { user } = useAuth();
  const { showSuccess, showError } = useAlertMigration();

  const [loading, setLoading] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const [refreshing, setRefreshing] = useState(false); // for refresh icon spin
  const [connecting, setConnecting] = useState({});
  const [platformStatus, setPlatformStatus] = useState({
    whatsapp: { configured: false, enabled: false, oauth_connected: false },
    messenger: { configured: false, enabled: false, oauth_connected: false },
    instagram: { configured: false, enabled: false, oauth_connected: false },
  });

  useEffect(() => {
    if (user?.user?.id) {
      fetchIntegrations();
    }
  }, [user]);

  // Handle OAuth callback messages
  useEffect(() => {
    const handleMessage = (event) => {
      console.log('Received message:', event.data, 'from origin:', event.origin);

      // Accept messages from any origin for OAuth callback
      if (event.data.type === 'OAUTH_SUCCESS') {
        console.log('OAuth success received:', event.data);
        const platform = event.data.platform;

        setConnecting(prev => ({
          ...prev,
          [platform]: false
        }));

        // Update the platform status immediately
        setPlatformStatus(prev => ({
          ...prev,
          [platform]: {
            ...prev[platform],
            oauth_connected: true,
            configured: true
          }
        }));

        // Refresh integrations data after a short delay
        setTimeout(() => {
          fetchIntegrations();
        }, 1000);

        showSuccess(`${platform.charAt(0).toUpperCase() + platform.slice(1)} connected successfully!`);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const fetchIntegrations = async () => {
    try {
      if (initialLoad) setLoading(true);
      setRefreshing(true);

      // Get OAuth integrations for Messenger and Instagram
      const response = await aiService.getSocialMediaIntegrations(user.user.id);

      // Get WhatsApp status separately (using old system)
      const whatsappResponse = await aiService.getWhatsAppStatus(user.user.id);

      if (response.success) {
        const platformStatus = response.data.platformStatus;

        // Update WhatsApp status from the old system
        if (whatsappResponse.success && whatsappResponse.data) {
          platformStatus.whatsapp = {
            configured: !!(whatsappResponse.data.whatsapp_business_account_id && whatsappResponse.data.whatsapp_phone_number_id),
            enabled: whatsappResponse.data.is_active || false,
            oauth_connected: false, // WhatsApp doesn't use OAuth
          };
        } else {
          platformStatus.whatsapp = {
            configured: false,
            enabled: false,
            oauth_connected: false,
          };
        }

        setPlatformStatus(platformStatus);
      } else {
        showError(response.error || "Failed to load integrations");
      }
    } catch (error) {
      console.error("Error fetching integrations:", error);
      showError("Failed to load integrations");
    } finally {
      setLoading(false);
      setInitialLoad(false);
      setTimeout(() => setRefreshing(false), 1000);
    }
  };

  const handlePlatformToggle = async (platform, enabled) => {
    try {
      setConnecting(prev => ({ ...prev, [platform]: true }));
      const response = await aiService.togglePlatformIntegration(user.user.id, platform, enabled);

      if (response.success) {
        setPlatformStatus(prev => ({
          ...prev,
          [platform]: { ...prev[platform], enabled }
        }));
        showSuccess(`${platform.charAt(0).toUpperCase() + platform.slice(1)} ${enabled ? 'enabled' : 'disabled'} successfully`);
      } else {
        showError(response.error || `Failed to toggle ${platform}`);
      }
    } catch (error) {
      console.error(`Error toggling ${platform}:`, error);
      showError(`Failed to toggle ${platform}`);
    } finally {
      setConnecting(prev => ({ ...prev, [platform]: false }));
    }
  };

  const handleOAuthConnect = async (platform) => {
    try {
      setConnecting(prev => ({ ...prev, [platform]: true }));

      console.log(`Starting OAuth flow for ${platform}...`);
      const result = await oauthService.startOAuthFlow(user.user.id, platform);
      console.log(`OAuth result for ${platform}:`, result);

      if (result) {
        showSuccess(`${platform.charAt(0).toUpperCase() + platform.slice(1)} connected successfully!`);
        // Wait a moment for the backend to process, then refresh
        setTimeout(async () => {
          await fetchIntegrations();
        }, 1000);
      }
    } catch (error) {
      console.error(`Error connecting ${platform}:`, error);
      showError(error.message || `Failed to connect ${platform}`);
    } finally {
      setConnecting(prev => ({ ...prev, [platform]: false }));
    }
  };

  const handleOAuthDisconnect = async (platform) => {
    try {
      setConnecting(prev => ({ ...prev, [platform]: true }));

      const response = await oauthService.disconnectPlatform(user.user.id, platform);

      if (response.success) {
        showSuccess(`${platform.charAt(0).toUpperCase() + platform.slice(1)} disconnected successfully!`);
        await fetchIntegrations();
      } else {
        showError(response.error || `Failed to disconnect ${platform}`);
      }
    } catch (error) {
      console.error(`Error disconnecting ${platform}:`, error);
      showError(error.message || `Failed to disconnect ${platform}`);
    } finally {
      setConnecting(prev => ({ ...prev, [platform]: false }));
    }
  };

  const PlatformCard = ({ platform, title, description, color }) => {
    const status = platformStatus[platform];
    const isConnecting = connecting[platform];

    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-full flex flex-col">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-lg ${color}`}>
              <MessageCircle className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              <p className="text-sm text-gray-600">{description}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {platform === "whatsapp" ? (
              // WhatsApp uses configured status instead of OAuth
              status.configured ? (
                <CircleCheck className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )
            ) : (
              // Messenger and Instagram use OAuth status
              status.oauth_connected ? (
                <CircleCheck className="w-5 h-5 text-green-500" />
              ) : (
                <XCircle className="w-5 h-5 text-red-500" />
              )
            )}

            {/* WhatsApp toggle is always disabled (greyed out) */}
            {platform === "whatsapp" && status.configured && (
              <button
                disabled
                title="WhatsApp toggling is managed in the WhatsApp Integration page."
                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 opacity-60 cursor-not-allowed"
                style={{ pointerEvents: 'none' }}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    status.enabled ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            )}
            {/* Messenger/Instagram toggles remain as before */}
            {platform !== "whatsapp" && status.oauth_connected && (
              <button
                onClick={() => handlePlatformToggle(platform, !status.enabled)}
                disabled={isConnecting}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  status.enabled ? "bg-blue-600" : "bg-gray-200"
                } ${isConnecting ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    status.enabled ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            )}
          </div>
        </div>

        <div className="flex flex-col justify-between h-full">
          <div className="space-y-3 h-full">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Connection:</span>
              <span className={`font-medium ${
                platform === "whatsapp"
                  ? (status.configured ? "text-green-600" : "text-red-600")
                  : (status.oauth_connected ? "text-green-600" : "text-red-600")
              }`}>
                {platform === "whatsapp"
                  ? (status.configured ? "Configured" : "Not Configured")
                  : (status.oauth_connected ? "Connected" : "Not Connected")
                }
              </span>
            </div>

            {((platform === "whatsapp" && status.configured) || (platform !== "whatsapp" && status.oauth_connected)) && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${
                  status.enabled
                    ? "text-green-600"
                    : "text-red-600"
                }`}>
                  {status.enabled ? "Active" : "Disabled"}
                </span>
              </div>
            )}
          </div>
          <div className="mt-4">
              {platform === "whatsapp" && (
                <div className="text-center">
                  <p className="text-sm text-gray-600 mb-3">
                    WhatsApp integration is managed separately using system tokens.
                  </p>
                  <a
                    href="/dashboard/whatsapp"
                    className="items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors w-full flex justify-center"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Configure WhatsApp
                  </a>
                </div>
              )}

              {platform === "messenger" && (
                <MessengerOAuthButton
                  isConnected={status.oauth_connected}
                  isLoading={isConnecting}
                  onConnect={() => handleOAuthConnect(platform)}
                  onDisconnect={() => handleOAuthDisconnect(platform)}
                  className="w-full"
                />
              )}

              {platform === "instagram" && (
                <InstagramOAuthButton
                  isConnected={status.oauth_connected}
                  isLoading={isConnecting}
                  onConnect={() => handleOAuthConnect(platform)}
                  onDisconnect={() => handleOAuthDisconnect(platform)}
                  className="w-full"
                />
              )}
            </div>
        </div>
      </div>
    );
  };

  if (loading && initialLoad) {
    return (
      <div className="flex items-center justify-center">
        <RotateCcw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex justify-between items-center flex-shrink-0">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Social Media Integrations</h2>
          <p className="text-gray-600">Connect your social media platforms to enable AI chatbot responses.</p>
        </div>
        <button
          onClick={fetchIntegrations}
          disabled={loading}
          className="btn-primary flex items-center gap-2 px-4 py-2"
        >
          <RotateCcw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {/* Info Card */}
      <div className="card flex-shrink-0 bg-blue-50">
        <div className="flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Platform Integration</p>
            <p><strong>Messenger & Instagram:</strong> Use easy OAuth integration - just click connect!</p>
            <p><strong>WhatsApp:</strong> Uses system tokens for enhanced security and is managed separately.</p>
          </div>
        </div>
      </div>

        {/* WhatsApp Notice */}
        <div className="card flex-shrink-0 bg-green-50">
        <div className="flex items-start gap-3">
          <MessageCircle className="w-5 h-5 text-green-600 mt-0.5" />
          <div className="text-sm text-green-800">
            <p className="font-medium mb-1">WhatsApp Business Integration</p>
            <p>WhatsApp uses system tokens for enhanced security and reliability. Configure your WhatsApp Business API settings in the dedicated WhatsApp Integration page for full control over your business messaging.</p>
            <div className="mt-2">
              <a
                href="/dashboard/whatsapp"
                className="inline-flex items-center text-green-700 hover:text-green-900 font-medium"
              >
                Configure WhatsApp Integration
                <ExternalLink className="w-3 h-3 ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Platform Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <PlatformCard
          platform="whatsapp"
          title="WhatsApp"
          description="WhatsApp Business API integration"
          color="bg-green-500"
        />
        <PlatformCard
          platform="messenger"
          title="Messenger"
          description="Facebook Messenger integration"
          color="bg-blue-500"
        />
        <PlatformCard
          platform="instagram"
          title="Instagram"
          description="Instagram Direct Messages integration"
          color="bg-pink-500"
        />
      </div>
    </div>
  );
}
