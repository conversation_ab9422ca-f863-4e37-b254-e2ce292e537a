import { useState, useEffect, useMemo, useRef } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../services/ai";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import { CenterLoader, InlineLoader } from "../../components/ui/loader";
import { checkMessageGapRestriction } from "../../utils/messageValidation";
import { Phone, Image as ImageIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select2";
import { Checkbox } from "../../components/ui/checkbox";
import Icon from "../../components/ui/icon";
import ContactTagManager from "../../components/ContactTagManager";

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:3000";

function capitalizeWords(str) {
  return str.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
}

// Auto-tag categories for color coding (matching backend)
const autoTagCategories = {
  "Lead Quality": ["hot_lead", "warm_lead", "cold_lead", "high_value_prospect"],
  "Customer Status": ["customer", "repeat_customer", "just_browsing"],
  "Buying Behavior": ["ready_to_buy", "impulse_buyer", "comparison_shopper", "research_phase"],
  "Decision Making": ["decision_maker", "needs_approval"],
  "Price Sensitivity": ["price_sensitive", "budget_conscious"],
  "Engagement": ["needs_follow_up", "follow_up_responsive", "urgent_inquiry"],
  "Support Needs": ["technical_questions", "objection_handling"]
};

// Get tag color based on category
function getTagColor(tag) {
  for (const [category, tags] of Object.entries(autoTagCategories)) {
    if (tags.includes(tag)) {
      switch (category) {
        case "Lead Quality": return "bg-green-50 text-green-700 border-green-200";
        case "Customer Status": return "bg-blue-50 text-blue-700 border-blue-200";
        case "Buying Behavior": return "bg-purple-50 text-purple-700 border-purple-200";
        case "Decision Making": return "bg-orange-50 text-orange-700 border-orange-200";
        case "Price Sensitivity": return "bg-yellow-50 text-yellow-700 border-yellow-200";
        case "Engagement": return "bg-red-50 text-red-700 border-red-200";
        case "Support Needs": return "bg-indigo-50 text-indigo-700 border-indigo-200";
        default: return "bg-gray-50 text-gray-700 border-gray-200";
      }
    }
  }
  return "bg-gray-50 text-gray-700 border-gray-200";
}

// Get intent category color
function getIntentColor(intent) {
  switch (intent) {
    case 'interested': return 'bg-green-50 text-green-700 border-green-200';
    case 'placed_order': return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'booking': return 'bg-purple-50 text-purple-700 border-purple-200';
    case 'support_inquiry': return 'bg-orange-50 text-orange-700 border-orange-200';
    case 'price_inquiry': return 'bg-yellow-50 text-yellow-700 border-yellow-200';
    case 'product_research': return 'bg-indigo-50 text-indigo-700 border-indigo-200';
    case 'comparison_shopping': return 'bg-pink-50 text-pink-700 border-pink-200';
    case 'ready_to_purchase': return 'bg-emerald-50 text-emerald-700 border-emerald-200';
    case 'not_interested': return 'bg-red-50 text-red-700 border-red-200';
    case 'no_follow_up': return 'bg-gray-50 text-gray-700 border-gray-200';
    default: return 'bg-gray-50 text-gray-700 border-gray-200';
  }
}

export default function Contacts() {
  const { user } = useAuth();

  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const { showError, showSuccess } = useAlertMigration();

  // Search and Filter State
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOrder, setSortOrder] = useState("recent");
  const [showActiveOnly, setShowActiveOnly] = useState(false);

  // Chat History State
  const [selectedContact, setSelectedContact] = useState(null);
  const [chatHistory, setChatHistory] = useState([]);
  const [loadingChat, setLoadingChat] = useState(false);
  const [chatError, setChatError] = useState("");

  // Edit Modal State
  const [showEditModal, setShowEditModal] = useState(false);
  const [showTagManager, setShowTagManager] = useState(false);
  const [editingContact, setEditingContact] = useState(null);
  const [editForm, setEditForm] = useState({
    name: "",
    tags: "",
    notes: "",
    isActive: true,
  });
  const [saving, setSaving] = useState(false);

  // Enhanced Message Input State
  const [messageInput, setMessageInput] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [messageType, setMessageType] = useState("text");
  const [isUploading, setIsUploading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [caption, setCaption] = useState("");
  const fileInputRef = useRef(null);

  // Human Takeover State
  const [humanTakeoverStatus, setHumanTakeoverStatus] = useState(null);
  const [isCheckingTakeover, setIsCheckingTakeover] = useState(false);
  const [isTogglingTakeover, setIsTogglingTakeover] = useState(false);

  // Chat Refresh State
  const [isRefreshingChat, setIsRefreshingChat] = useState(false);
  const [showRefreshSpinner, setShowRefreshSpinner] = useState(false);

  // Auto-refresh State
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(true);
  const [autoRefreshInterval] = useState(5000); // 5 seconds default
  const autoRefreshIntervalRef = useRef(null);
  const lastMessageCountRef = useRef(0);
  const chatContainerRef = useRef(null);
  const shouldScrollToBottomRef = useRef(true);

  // Message gap restriction
  const [messageGapRestriction, setMessageGapRestriction] = useState({
    isBlocked: false,
    message: null,
    daysSinceLastMessage: 0
  });

  const chatEndRef = useRef(null);

  const fetchContacts = async () => {
    const userId = user?.user?.id || user?.profile?.id || user?.id;
    if (!userId) return;

    try {
      const data = await aiService.getContacts({ authId: userId, limit: 100 });

      if (data.success) {
        setContacts(data.data);
      } else {
        showError(data.error);
      }
    } catch {
      showError("Failed to fetch contacts");
    } finally {
      setLoading(false);
    }
  };

  const fetchChatHistory = async (contact, options = {}) => {
    if (!contact) return;

    const {
      preserveScrollPosition = false,
      isAutoRefresh = false,
      showLoading = true,
    } = options;

    if (showLoading) {
      setLoadingChat(true);
    }
    setChatError("");
    const userId = user?.user?.id || user?.profile?.id || user?.id;

    // Store current scroll position if preserving
    let scrollTop = 0;
    let scrollHeight = 0;
    if (preserveScrollPosition && chatContainerRef.current) {
      scrollTop = chatContainerRef.current.scrollTop;
      scrollHeight = chatContainerRef.current.scrollHeight;
    }

    try {
      // Get chat history with media information
      const data = await aiService.getChatHistory(
        userId,
        contact.phone_number,
        50,
        0,
        true, // includeMedia flag
      );

      if (data.success) {
        // Process media information for each message
        const processedMessages = data.data.map((message) => {
          if (message.media_filename) {
            return {
              ...message,
              hasMedia: true,
              mediaInfo: {
                filename: message.media_filename,
                fileUrl:
                  message.media_url ||
                  `/api/ai/media/${message.media_filename}`,
                mediaType: message.media_type,
                mimeType: message.mime_type,
                fileSize: message.file_size,
              },
            };
          }
          return message;
        });

        // Check if there are new messages for auto-refresh
        const newMessageCount = processedMessages.length;
        const hasNewMessages =
          isAutoRefresh && newMessageCount > lastMessageCountRef.current;

        setChatHistory(processedMessages);
        lastMessageCountRef.current = newMessageCount;

        // Check message gap restriction
        const gapCheck = checkMessageGapRestriction(processedMessages);
        setMessageGapRestriction(gapCheck);

        // Handle scrolling behavior
        setTimeout(() => {
          if (preserveScrollPosition && chatContainerRef.current) {
            // Restore scroll position
            const newScrollHeight = chatContainerRef.current.scrollHeight;
            const scrollDiff = newScrollHeight - scrollHeight;
            chatContainerRef.current.scrollTop = scrollTop + scrollDiff;
          } else if (shouldScrollToBottomRef.current || hasNewMessages) {
            // Scroll to bottom for new messages or when explicitly requested
            chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
          }
        }, 100);
      } else {
        setChatError(data.error || "Failed to load chat history");
        setChatHistory([]);
      }
    } catch {
      setChatError("Failed to load chat history");
      setChatHistory([]);
    } finally {
      if (showLoading) {
        setLoadingChat(false);
      }
    }
  };

  const handleContactClick = (contact) => {
    setSelectedContact(contact);
    setHumanTakeoverStatus(null); // Clear previous status
    shouldScrollToBottomRef.current = true; // Always scroll to bottom when selecting new contact
    fetchChatHistory(contact);
    checkHumanTakeoverStatus(contact);
  };

  const checkHumanTakeoverStatus = async (contact) => {
    if (!contact) return;

    setIsCheckingTakeover(true);
    const userId = user?.user?.id || user?.profile?.id || user?.id;

    try {
      const data = await aiService.getHumanTakeoverStatus(
        userId,
        contact.phone_number,
      );
      if (data.success) {
        setHumanTakeoverStatus(data.data);
      }
    } catch (error) {
      console.error("Error checking human takeover status:", error);
      setHumanTakeoverStatus(null);
    } finally {
      setIsCheckingTakeover(false);
    }
  };

  const toggleHumanTakeover = async () => {
    if (!selectedContact) return;

    setIsTogglingTakeover(true);
    const userId = user?.user?.id || user?.profile?.id || user?.id;

    try {
      let data;
      if (humanTakeoverStatus?.isActive) {
        data = await aiService.endHumanTakeover(
          userId,
          selectedContact.phone_number,
        );
      } else {
        data = await aiService.startHumanTakeover(
          userId,
          selectedContact.phone_number,
          30,
        );
      }

      if (data.success) {
        showSuccess(data.data.message);
        checkHumanTakeoverStatus(selectedContact);
      } else {
        showError(data.error || "Failed to toggle human takeover");
      }
    } catch (error) {
      console.error("Error toggling human takeover:", error);
      showError("Failed to toggle human takeover");
    } finally {
      setIsTogglingTakeover(false);
    }
  };

  const updateContact = async () => {
    if (!editingContact) return;

    setSaving(true);

    try {
      const userId = user?.user?.id || user?.profile?.id || user?.id;
      const data = await aiService.updateContact(editingContact.id, {
        authId: userId,
        name: editForm.name || null,
        tags: editForm.tags
          ? editForm.tags.split(",").map((tag) => tag.trim())
          : [],
        notes: editForm.notes || null,
        isActive: editForm.isActive,
      });

      if (data.success) {
        showSuccess("Contact updated successfully!");

        // Refresh contacts list
        await fetchContacts();

        // Update selectedContact if it's the same contact that was edited
        if (selectedContact && selectedContact.id === editingContact.id) {
          // Create updated contact object with new data
          const updatedContact = {
            ...selectedContact,
            name: editForm.name || null,
            tags: editForm.tags
              ? editForm.tags.split(",").map((tag) => tag.trim())
              : [],
            notes: editForm.notes || null,
            is_active: editForm.isActive,
          };
          setSelectedContact(updatedContact);
        }

        setShowEditModal(false);
        setEditingContact(null);
      } else {
        showError(data.error);
      }
    } catch {
      showError("Failed to update contact");
    } finally {
      setSaving(false);
    }
  };

  const openEditModal = (contact) => {
    setEditingContact(contact);
    setEditForm({
      name: contact.name || "",
      tags: contact.tags ? contact.tags.join(", ") : "",
      notes: contact.notes || "",
      isActive: contact.is_active !== false,
    });
    setShowEditModal(true);
  };

  const openTagManager = (contact) => {
    setEditingContact(contact);
    setShowTagManager(true);
  };

  const handleContactUpdate = (updatedContact) => {
    // Update the contact in the contacts list
    setContacts(prev =>
      prev.map(c => c.id === updatedContact.id ? updatedContact : c)
    );

    // Update selected contact if it's the same one
    if (selectedContact && selectedContact.id === updatedContact.id) {
      setSelectedContact(updatedContact);
    }
  };

  const formatChatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
    );

    const time = date.toLocaleTimeString("en-MY", {
      hour: "2-digit",
      minute: "2-digit",
    });

    if (messageDate.getTime() === today.getTime()) {
      // For today's messages, show "Today" + time
      return `Today ${time}`;
    } else {
      // For other days, show date + time
      const dateStr = date.toLocaleDateString("en-MY", {
        month: "short",
        day: "numeric",
      });
      return `${dateStr} ${time}`;
    }
  };

  const formatPhoneNumber = (phone) => {
    if (!phone) return "Unknown";
    if (phone.startsWith("+")) return phone;
    return `+${phone}`;
  };

  const getActivityStatus = (lastMessageAt) => {
    if (!lastMessageAt) return "inactive";
    const lastActivity = new Date(lastMessageAt);
    const now = new Date();
    const daysDiff = (now - lastActivity) / (1000 * 60 * 60 * 24);

    if (daysDiff <= 1) return "today";
    if (daysDiff <= 7) return "week";
    if (daysDiff <= 30) return "month";
    return "inactive";
  };

  const filteredAndSortedContacts = useMemo(() => {
    let result = [...contacts];

    // Apply filters
    if (showActiveOnly) {
      result = result.filter((contact) => contact.is_active !== false);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (contact) =>
          (contact.name && contact.name.toLowerCase().includes(query)) ||
          (contact.phone_number && contact.phone_number.includes(query)) ||
          (contact.notes && contact.notes.toLowerCase().includes(query)) ||
          (contact.tags &&
            contact.tags.some((tag) => tag.toLowerCase().includes(query))),
      );
    }

    // Apply sorting
    result.sort((a, b) => {
      switch (sortOrder) {
        case "recent":
          return (
            new Date(b.last_message_at || 0) - new Date(a.last_message_at || 0)
          );
        case "oldest":
          return new Date(a.created_at || 0) - new Date(b.created_at || 0);
        case "name":
          return (a.name || a.phone_number || "").localeCompare(
            b.name || b.phone_number || "",
          );
        case "messages":
          return (b.total_messages || 0) - (a.total_messages || 0);
        default:
          return 0;
      }
    });

    return result;
  }, [contacts, searchQuery, sortOrder, showActiveOnly]);

  useEffect(() => {
    if (user) {
      fetchContacts();
    }
  }, [user]);

  // Auto-refresh effect for chat history
  useEffect(() => {
    if (!selectedContact || !autoRefreshEnabled) {
      return;
    }

    // Clear any existing interval
    if (autoRefreshIntervalRef.current) {
      clearInterval(autoRefreshIntervalRef.current);
    }

    // Set up new interval
    autoRefreshIntervalRef.current = setInterval(() => {
      if (selectedContact && autoRefreshEnabled) {
        fetchChatHistory(selectedContact, {
          preserveScrollPosition: true,
          isAutoRefresh: true,
          showLoading: false,
        });
      }
    }, autoRefreshInterval);

    // Cleanup function
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [selectedContact, autoRefreshEnabled, autoRefreshInterval]);

  // Cleanup auto-refresh on unmount
  useEffect(() => {
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
      }
    };
  }, []);

  // Handle scroll position detection
  const handleChatScroll = () => {
    if (!chatContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100; // Within 100px of bottom

    // Update whether we should auto-scroll to bottom
    shouldScrollToBottomRef.current = isNearBottom;
  };

  // File handling functions
  const handleFileSelect = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Reset states
    setSelectedFile(file);
    setMessageType(
      file.type.startsWith("image/")
        ? "image"
        : file.type.startsWith("video/")
          ? "video"
          : file.type.startsWith("audio/")
            ? "audio"
            : "text",
    );

    // Create preview for images
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFilePreview(reader.result);
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith("video/")) {
      setFilePreview(URL.createObjectURL(file));
    }
  };

  // Add a reset states function to keep it DRY
  const resetMessageStates = () => {
    setMessageInput("");
    setSelectedFile(null);
    setFilePreview(null);
    setMessageType("text");
    setCaption("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile || !selectedContact) return null;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("media", selectedFile);
      formData.append(
        "authId",
        user?.user?.id || user?.profile?.id || user?.id,
      );
      formData.append("phoneNumber", selectedContact.phone_number);

      // Add caption if provided
      if (caption && caption.trim()) {
        formData.append("caption", caption.trim());
      }

      console.log("📎 Uploading file:", {
        fileName: selectedFile.name,
        mimeType: selectedFile.type,
        size: selectedFile.size,
        userId: user?.user?.id || user?.profile?.id || user?.id,
        phoneNumber: selectedContact.phone_number,
        caption: caption || "No caption",
      });

      const response = await fetch(`${API_BASE_URL}/api/ai/upload-media`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();
      console.log("📎 File upload response:", result);

      if (!result.success) {
        console.error("📎 File upload failed:", result.error);
        resetMessageStates(); // Reset states on upload error
        throw new Error(result.error);
      }

      // The upload-media endpoint uploads AND sends the message in one go
      const messageId = result.data?.message?.messages?.[0]?.id;
      console.log("📎 File sent successfully! Message ID:", messageId);

      if (!messageId) {
        console.error("📎 No message ID received from upload response");
        console.error("📎 Response structure:", result.data);
        resetMessageStates(); // Reset states if no message ID
        throw new Error("No message ID received from file upload");
      }

      // Since upload-media already sent the message, we don't need to send it again
      // Just reset states and refresh chat history
      resetMessageStates();
      shouldScrollToBottomRef.current = true; // Scroll to bottom for new message
      await fetchChatHistory(selectedContact);
      return "MESSAGE_SENT"; // Special return value to indicate message was sent
    } catch (error) {
      console.error("Error uploading file:", error);
      resetMessageStates(); // Reset states on any error
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Message sending function
  const sendMessage = async () => {
    if (!selectedContact || (!messageInput.trim() && !selectedFile)) return;

    // Check message gap restriction first
    if (messageGapRestriction.isBlocked) {
      showError(messageGapRestriction.message);
      return;
    }

    setIsSending(true);
    try {
      const userId = user?.user?.id || user?.profile?.id || user?.id;
      let mediaInfo = null;

      // Upload file if exists
      if (selectedFile) {
        try {
          const uploadResult = await handleFileUpload();
          if (uploadResult === "MESSAGE_SENT") {
            // File upload already sent the message, we're done
            return;
          }
          mediaInfo = uploadResult;

          console.log("Final mediaInfo:", mediaInfo);
        } catch (error) {
          console.error("Error uploading media:", error);
          resetMessageStates(); // Reset states on upload error
          return;
        }
      }

      // Prepare message payload
      const messagePayload = {
        authId: userId,
        phoneNumber: selectedContact.phone_number,
        messageType,
        content: messageType === "text" ? messageInput : undefined,
        caption: caption || undefined,
      };

      // Add media ID
      if (mediaInfo?.mediaId) {
        messagePayload.mediaId = mediaInfo.mediaId;
      }

      console.log("Sending message payload:", messagePayload);

      // Send message
      const response = await fetch(`${API_BASE_URL}/api/ai/send-message`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(messagePayload),
      });

      const result = await response.json();
      console.log("Send message response:", result);

      if (!result.success) {
        resetMessageStates(); // Reset states on send error
        throw new Error(result.error);
      }

      // Reset states after successful send
      resetMessageStates();

      // Refresh chat history and scroll to bottom for new message
      shouldScrollToBottomRef.current = true;
      await fetchChatHistory(selectedContact);
    } catch (error) {
      console.error("Error sending message:", error);
      resetMessageStates(); // Reset states on any error
    } finally {
      setIsSending(false);
    }
  };

  // Update the remove button click handler to use the reset function
  const handleRemoveMedia = () => {
    resetMessageStates();
  };

  const handleRefreshChat = async () => {
    if (!selectedContact) return;

    setIsRefreshingChat(true);
    setShowRefreshSpinner(true);
    shouldScrollToBottomRef.current = true; // Force scroll to bottom on manual refresh
    try {
      await fetchChatHistory(selectedContact);
    } finally {
      setIsRefreshingChat(false);
      // Keep spinner visible for a short duration after loading completes
      setTimeout(() => {
        setShowRefreshSpinner(false);
      }, 500);
    }
  };

  if (showLoadingState) {
    return <PageSkeleton cardType="contacts" cardCount={8} />;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Contact Management
          </h2>
          <p className="text-gray-600">
            View and manage your customer contacts and chat history.
          </p>
        </div>
      </div>

      {/* Error and success messages now handled by global AlertContainer */}

      {/* Search and Filters */}
      <div className="card">
        <div className="flex items-center gap-2 mb-3">
          <Icon name="Filter" className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <label
              htmlFor="search"
              className="block text-sm font-medium text-gray-700"
            >
              Search Contacts
            </label>
            <input
              type="search"
              placeholder="Search contacts by name, phone, notes, or tags..."
              className="input-field pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <label
              htmlFor="sort"
              className="block text-sm font-medium text-gray-700"
            >
              Sort by
            </label>
            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="input-field">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Recent Activity</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="messages">Most Messages</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Options
            </label>
            <div className="flex items-center gap-3 border border-gray-200 border-2 rounded-lg p-3">
              <Checkbox
                checked={showActiveOnly}
                onCheckedChange={setShowActiveOnly}
                className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 data-[state=checked]:text-white w-6 h-6"
              />
              <span className="text-md text-gray-700 cursor-pointer truncate">
                Show active contacts only
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Side by Side Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Contacts List - Left Side */}
        <div className="lg:col-span-1">
          <div className="card h-[calc(100vh-430px)] min-h-[500px] flex flex-col">
            <div className="flex justify-between items-center p-4 border-b border-gray-200 flex-shrink-0">
              <h3 className="text-lg font-semibold text-gray-900">
                Contacts ({filteredAndSortedContacts.length})
              </h3>
              {filteredAndSortedContacts.length > 0 && (
                <span className="text-sm text-gray-500">
                  {contacts.length > filteredAndSortedContacts.length &&
                    `${filteredAndSortedContacts.length} of ${contacts.length}`}
                </span>
              )}
            </div>

            <div className="flex-1 overflow-y-auto">
              {filteredAndSortedContacts.length === 0 ? (
                <div className="flex items-center justify-center h-full text-center p-6">
                  {contacts.length === 0 ? (
                    <div>
                      <Icon
                        name="User"
                        className="w-12 h-12 text-gray-400 mx-auto mb-3"
                      />
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        No contacts yet
                      </h4>
                      <p className="text-gray-600 text-sm">
                        Contacts will appear here when people start messaging
                        your chatbot.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <Icon
                        name="User"
                        className="w-12 h-12 text-gray-400 mx-auto mb-3"
                      />
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        No matching contacts
                      </h4>
                      <p className="text-gray-600 text-sm">
                        Try adjusting your search or filters
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-2 p-4">
                  {filteredAndSortedContacts.map((contact) => {
                    const activityStatus = getActivityStatus(
                      contact.last_message_at,
                    );
                    const isSelected = selectedContact?.id === contact.id;
                    const hasAIAnalysis =
                      (contact.ai_engagement_score !== undefined && contact.ai_engagement_score !== null) ||
                      !!contact.ai_intent_category ||
                      (contact.auto_tags && contact.auto_tags.length > 0) ||
                      contact.follow_up_enabled;
                    return (
                      <div
                        key={contact.id}
                        className={`group relative rounded-xl transition-all duration-200 border overflow-hidden ${
                          isSelected
                            ? "border-blue-200 bg-blue-50 shadow-lg ring-2 ring-blue-100"
                            : "border-gray-200 hover:border-blue-200 hover:bg-gray-50 hover:shadow-md bg-white"
                        }`}
                      >
                        {/* Main content - clickable area */}
                        <div
                          className="cursor-pointer p-4"
                          onClick={() => handleContactClick(contact)}
                        >
                          <div className="flex items-start gap-4">
                            {/* Avatar with status indicator */}
                            <div className="relative flex-shrink-0">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-sm">
                                {(contact.name || contact.phone_number || "U")
                                  .charAt(0)
                                  .toUpperCase()}
                              </div>
                              {/* Online status indicator */}
                              <div
                                className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                                  activityStatus === "today"
                                    ? "bg-green-500"
                                    : activityStatus === "week"
                                      ? "bg-blue-500"
                                      : activityStatus === "month"
                                        ? "bg-yellow-500"
                                        : "bg-gray-400"
                                }`}
                              />
                            </div>

                            <div className="flex-1 min-w-0">
                              {/* Header with name and activity status */}
                              <div className="flex items-start justify-between gap-3 mb-2 pr-12">
                                <div className="min-w-0 flex-1">
                                  <h4 className="font-semibold text-gray-900 text-base truncate">
                                    {contact.name || "Unnamed Contact"}
                                  </h4>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Phone className="w-3.5 h-3.5 text-gray-400" />
                                    <p className="text-sm text-gray-600 font-medium">
                                      {formatPhoneNumber(contact.phone_number)}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              {/* Stats row */}
                              <div className="flex items-center gap-4 mb-3">
                                <div className="flex items-center gap-1.5">
                                  <Icon
                                    name="MessageSquare"
                                    className="w-4 h-4 text-gray-400"
                                  />
                                  <span className="text-sm text-gray-600 font-medium">
                                    {contact.total_messages || 0} messages
                                  </span>
                                </div>
                                {contact.last_message_at && (
                                  <div className="flex items-center gap-1.5">
                                    <Icon
                                      name="Clock"
                                      className="w-4 h-4 text-gray-400"
                                    />
                                    <span className="text-sm text-gray-500">
                                      {formatChatTime(contact.last_message_at)}
                                    </span>
                                  </div>
                                )}
                              </div>

                              {/* Manual Tags Section */}
                              {contact.tags && contact.tags.length > 0 && (
                                <div className="mb-3">
                                  <div className="flex items-center gap-1 mb-1.5">
                                    <Icon name="Tag" className="w-3 h-3 text-emerald-600" />
                                    <span className="text-xs text-gray-600 font-medium">Manual Tags</span>
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {contact.tags.map((tag, index) => (
                                      <span
                                        key={index}
                                        className="inline-flex items-center px-2 py-0.5 bg-emerald-50 text-emerald-700 rounded-md text-xs font-medium border border-emerald-200"
                                      >
                                        {tag.charAt(0).toUpperCase() + tag.slice(1)}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* AI Analysis Section */}
                              {hasAIAnalysis && (
                                <div className="mb-3">
                                  <div className="flex items-center gap-1 mb-1.5">
                                    <Icon name="Brain" className="w-3 h-3 text-blue-600" />
                                    <span className="text-xs text-gray-600 font-medium">AI Analysis</span>
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {/* Engagement Score */}
                                    {contact.ai_engagement_score !== undefined && contact.ai_engagement_score !== null && (
                                      <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${
                                        contact.ai_engagement_score >= 0.7 ? 'bg-green-50 text-green-700 border-green-200' :
                                        contact.ai_engagement_score >= 0.4 ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                                        'bg-red-50 text-red-700 border-red-200'
                                      }`}>
                                        <Icon name="TrendingUp" className="w-3 h-3 mr-1" />
                                        {(contact.ai_engagement_score * 100).toFixed(0)}%
                                      </span>
                                    )}

                                    {/* AI Intent Category */}
                                    {contact.ai_intent_category && (
                                      <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getIntentColor(contact.ai_intent_category)}`}>
                                        {capitalizeWords(contact.ai_intent_category)}
                                      </span>
                                    )}

                                    {/* Follow-up Status */}
                                    {contact.follow_up_enabled && (
                                      <span className="inline-flex items-center px-2 py-0.5 bg-green-50 text-green-700 rounded-md text-xs font-medium border border-green-200">
                                        <Icon name="MessageCircle" className="w-3 h-3 mr-1" />
                                        Follow-up
                                      </span>
                                    )}

                                    {/* AI Auto Tags - Show first 4 tags with color coding */}
                                    {contact.auto_tags && contact.auto_tags.length > 0 && (
                                      <>
                                        {contact.auto_tags.map((tag, index) => (
                                          <span
                                            key={index}
                                            className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getTagColor(tag)}`}
                                          >
                                            {capitalizeWords(tag)}
                                          </span>
                                        ))}
                                      </>
                                    )}
                                  </div>
                                </div>
                              )}

                              {/* Notes */}
                              {contact.notes && (
                                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                                  <p className="text-sm text-amber-800 line-clamp-2">
                                    <span className="font-medium">Note:</span>{" "}
                                    {contact.notes}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Action buttons */}
                        <div className="absolute top-4 right-4 flex gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              openTagManager(contact);
                            }}
                            className="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-100 rounded-lg transition-all duration-200 bg-white shadow-sm border border-gray-200 hover:border-purple-300"
                            title="AI Tags & Follow-up"
                          >
                            <Icon name="Brain" className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              openEditModal(contact);
                            }}
                            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded-lg transition-all duration-200 bg-white shadow-sm border border-gray-200 hover:border-blue-300"
                            title="Edit contact"
                          >
                            <Icon name="Edit" className="w-4 h-4" />
                          </button>
                        </div>

                        {/* Selection indicator */}
                        {isSelected && (
                          <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500" />
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Chat History - Right Side */}
        <div className="lg:col-span-2">
          <div className="card h-[calc(100vh-430px)] min-h-[500px] flex flex-col">
            {selectedContact ? (
              <>
                {/* Chat Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {(
                        selectedContact.name ||
                        selectedContact.phone_number ||
                        "U"
                      )
                        .charAt(0)
                        .toUpperCase()}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {selectedContact.name || "Unnamed Contact"}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {formatPhoneNumber(selectedContact.phone_number)}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <div
                          className={`w-2 h-2 rounded-full ${humanTakeoverStatus?.isActive ? "bg-green-500" : "bg-gray-400"}`}
                        />
                        <span className="text-xs text-gray-500">
                          {humanTakeoverStatus?.isActive
                            ? "Human Mode"
                            : "AI Mode"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {/* Auto-refresh Toggle */}
                    <button
                      onClick={() => setAutoRefreshEnabled(!autoRefreshEnabled)}
                      className={`p-2 rounded-lg transition-colors ${
                        autoRefreshEnabled
                          ? "bg-green-100 text-green-600 hover:bg-green-200"
                          : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      }`}
                      title={
                        autoRefreshEnabled
                          ? "Auto-refresh enabled"
                          : "Auto-refresh disabled"
                      }
                    >
                      <Icon
                        name={autoRefreshEnabled ? "Play" : "Pause"}
                        className="w-4 h-4"
                      />
                    </button>

                    {/* Manual Refresh Chat Button */}
                    <button
                      onClick={handleRefreshChat}
                      disabled={isRefreshingChat || loadingChat}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Refresh chat history"
                    >
                      <Icon
                        name="RotateCcw"
                        className={`w-5 h-5 ${showRefreshSpinner ? "animate-spin" : ""}`}
                      />
                    </button>

                    {/* Human Takeover Toggle */}
                    <button
                      onClick={toggleHumanTakeover}
                      disabled={isCheckingTakeover || isTogglingTakeover}
                      className={`p-2 rounded-lg transition-colors ${
                        humanTakeoverStatus?.isActive
                          ? "bg-green-100 text-green-600 hover:bg-green-200"
                          : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                      title={
                        humanTakeoverStatus?.isActive
                          ? "End human takeover (resume AI)"
                          : "Start human takeover (pause AI)"
                      }
                    >
                      {isTogglingTakeover ? (
                        <Icon name="Loader2" className="w-5 h-5 animate-spin" />
                      ) : humanTakeoverStatus?.isActive ? (
                        <Icon name="User" className="w-5 h-5" />
                      ) : (
                        <Icon name="MessageSquare" className="w-5 h-5" />
                      )}
                    </button>

                    {/* Edit Contact Button */}
                    <button
                      onClick={() => openEditModal(selectedContact)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="Edit contact"
                    >
                      <Icon name="User" className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Human Takeover Banner */}
                {humanTakeoverStatus?.isActive && (
                  <div className="bg-green-50 border border-green-200 p-3 mx-4 mt-4 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Icon name="User" className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">
                        Human Mode Active
                      </span>
                    </div>
                    <p className="text-xs text-green-700 mt-1">
                      AI responses are paused. You're now handling this
                      conversation manually.
                    </p>
                  </div>
                )}

                {/* Chat Messages */}
                <div
                  ref={chatContainerRef}
                  className="flex-1 overflow-y-auto p-4 space-y-4"
                  onScroll={handleChatScroll}
                >
                  {loadingChat ? (
                    <div className="flex items-center justify-center h-full">
                      <CenterLoader size="md" />
                      <span className="ml-2 text-gray-600">
                        Loading chat history...
                      </span>
                    </div>
                  ) : chatError ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <Icon
                          name="MessageSquare"
                          className="w-12 h-12 text-gray-400 mx-auto mb-2"
                        />
                        <p className="text-red-600 mb-2">{chatError}</p>
                        <button
                          onClick={() => fetchChatHistory(selectedContact)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          Try again
                        </button>
                      </div>
                    </div>
                  ) : chatHistory.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-center">
                      <div>
                        <Icon
                          name="MessageSquare"
                          className="w-12 h-12 text-gray-400 mx-auto mb-2"
                        />
                        <p className="text-gray-600">No messages yet</p>
                        <p className="text-sm text-gray-500">
                          Start a conversation with this contact
                        </p>
                      </div>
                    </div>
                  ) : (
                    <>
                      {[...chatHistory].reverse().map((message, index) => (
                        <div
                          key={message.id || index}
                          className={`flex ${message.role === "user" ? "justify-start" : "justify-end"}`}
                        >
                          <div
                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                              message.role === "user"
                                ? "bg-gray-100 text-gray-900"
                                : "bg-blue-600 text-white"
                            }`}
                          >
                            {/* Media content */}
                            {message.hasMedia && message.mediaInfo && (
                              <div className="mb-2">
                                {message.mediaInfo.mediaType === "image" && (
                                  <img
                                    src={message.mediaInfo.fileUrl}
                                    alt="Sent image"
                                    className="rounded-lg max-w-full cursor-pointer hover:opacity-90 transition-opacity"
                                    onClick={() =>
                                      window.open(
                                        message.mediaInfo.fileUrl,
                                        "_blank",
                                      )
                                    }
                                  />
                                )}
                                {message.mediaInfo.mediaType === "video" && (
                                  <video
                                    controls
                                    className="rounded-lg max-w-full"
                                    src={message.mediaInfo.fileUrl}
                                  >
                                    Your browser does not support the video tag.
                                  </video>
                                )}
                                {message.mediaInfo.mediaType === "audio" && (
                                  <audio
                                    controls
                                    className="w-full"
                                    src={message.mediaInfo.fileUrl}
                                  >
                                    Your browser does not support the audio tag.
                                  </audio>
                                )}
                              </div>
                            )}

                            <p className="text-sm whitespace-pre-wrap break-words leading-relaxed">
                              {message.content}
                            </p>
                            <p
                              className={`text-xs mt-1 ${
                                message.role === "user"
                                  ? "text-gray-500"
                                  : "text-blue-100"
                              }`}
                            >
                              {formatChatTime(message.timestamp)}
                            </p>
                          </div>
                        </div>
                      ))}
                      <div ref={chatEndRef} />
                    </>
                  )}
                </div>

                {/* Message Input with Media Support */}
                <div className="border-t border-gray-200 p-4">
                  {/* File Preview */}
                  {filePreview && (
                    <div className="mb-4 relative">
                      <div className="rounded-lg border border-gray-200 p-3">
                        {messageType === "image" && (
                          <img
                            src={filePreview}
                            alt="Selected image"
                            className="max-h-40 rounded"
                          />
                        )}
                        {messageType === "video" && (
                          <video
                            src={filePreview}
                            className="max-h-40 rounded"
                            controls
                          />
                        )}
                        {/* Caption input for images and videos */}
                        {(messageType === "image" ||
                          messageType === "video") && (
                          <input
                            type="text"
                            value={caption}
                            onChange={(e) => setCaption(e.target.value)}
                            placeholder="Add a caption..."
                            className="input-field mt-2 text-sm"
                          />
                        )}
                        {/* Remove button */}
                        <button
                          onClick={handleRemoveMedia}
                          className="absolute top-2 right-2 p-1 bg-gray-800/50 hover:bg-gray-800/70 rounded-full text-white"
                        >
                          <Icon name="X" className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Message Gap Restriction Warning */}
                  {messageGapRestriction.isBlocked && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <p className="text-sm text-red-700 font-medium">
                          {messageGapRestriction.message}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Message Input */}
                  <div className="bg-white border border-gray-200 rounded-2xl p-3 shadow-sm">
                    <div className="flex items-center gap-3">
                      {/* Text Input */}
                      <div className="flex-1">
                        <textarea
                          value={messageInput}
                          onChange={(e) => setMessageInput(e.target.value)}
                          placeholder="Type a message..."
                          className="w-full px-4 pt-3 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none resize-none min-h-[44px] max-h-32 overflow-y-auto text-md leading-6"
                          rows="1"
                          onKeyDown={(e) => {
                            if (e.key === "Enter" && !e.shiftKey) {
                              e.preventDefault();
                              sendMessage();
                            }
                          }}
                          onInput={(e) => {
                            // Auto-resize the textarea
                            e.target.style.height = "auto";
                            e.target.style.height =
                              Math.min(e.target.scrollHeight, 128) + "px";
                          }}
                        />
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        {/* File Upload */}
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*,video/*"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="p-2.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Send image or video"
                          disabled={isUploading || isSending}
                        >
                          <Icon name="Paperclip" className="w-6 h-6" />
                        </button>

                        {/* Send Button */}
                        <button
                          onClick={sendMessage}
                          className={`p-2.5 px-15 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center min-w-[44px] h-[44px] ${
                            messageGapRestriction.isBlocked
                              ? "bg-red-500 text-white hover:bg-red-600"
                              : "bg-blue-600 text-white hover:bg-blue-700"
                          }`}
                          disabled={
                            (!messageInput.trim() && !selectedFile) ||
                            isUploading ||
                            isSending ||
                            messageGapRestriction.isBlocked
                          }
                          title={messageGapRestriction.isBlocked ? messageGapRestriction.message : ""}
                        >
                          {isUploading || isSending ? (
                            <Icon
                              name="Loader2"
                              className="w-5 h-5 animate-spin"
                            />
                          ) : (
                            <Icon name="Send" className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                  {/* Helper Text */}
                  <div className="mt-3">
                    <p className="text-sm text-gray-400">
                      {messageType === "text"
                        ? "Press Enter to send"
                        : `Sending ${messageType} message`}
                    </p>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full text-center">
                <div>
                  <Icon
                    name="MessageSquare"
                    className="w-16 h-16 text-gray-400 mx-auto mb-4"
                  />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Select a contact
                  </h3>
                  <p className="text-gray-600">
                    Choose a contact from the list to view their chat history
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Edit Contact Modal */}
      {showEditModal && editingContact && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/20 backdrop-blur-sm"
            onClick={() => {
              if (!saving) {
                setShowEditModal(false);
                setEditingContact(null);
              }
            }}
          />
          {/* Modal */}
          <div className="relative h-full flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">
                  Edit Contact
                </h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingContact(null);
                  }}
                  className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
                  disabled={saving}
                >
                  <Icon name="X" className="w-5 h-5 text-gray-400" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={editForm.name}
                    onChange={(e) =>
                      setEditForm((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="Contact name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tags (comma-separated)
                  </label>
                  <input
                    type="text"
                    className="input-field"
                    value={editForm.tags}
                    onChange={(e) =>
                      setEditForm((prev) => ({ ...prev, tags: e.target.value }))
                    }
                    placeholder="vip, customer, support"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    rows="3"
                    className="input-field resize-none"
                    value={editForm.notes}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    placeholder="Add notes about this contact..."
                  />
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={editForm.isActive}
                    onChange={(e) =>
                      setEditForm((prev) => ({
                        ...prev,
                        isActive: e.target.checked,
                      }))
                    }
                    className="sr-only"
                    disabled={saving}
                  />
                  <label
                    htmlFor="isActive"
                    className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 cursor-pointer ${
                      editForm.isActive
                        ? "bg-blue-600 border-blue-600"
                        : "bg-white border-gray-300 hover:border-blue-400"
                    } ${saving ? "opacity-60 cursor-not-allowed" : ""}`}
                  >
                    {editForm.isActive && (
                      <Icon name="Check" className="w-3 h-3 text-white" />
                    )}
                  </label>
                  <label
                    htmlFor="isActive"
                    className="text-sm text-gray-700 cursor-pointer"
                  >
                    Contact is active (can receive responses)
                  </label>
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    setEditingContact(null);
                  }}
                  className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={saving}
                >
                  Cancel
                </button>
                <button
                  onClick={updateContact}
                  className="flex-1 bg-blue-600 text-white px-4 py-2.5 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <InlineLoader />
                      Saving...
                    </>
                  ) : (
                    <>Save Changes</>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Tag Manager Modal */}
      {showTagManager && editingContact && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/20 backdrop-blur-sm"
            onClick={() => {
              setShowTagManager(false);
              setEditingContact(null);
            }}
          />
          {/* Modal */}
          <div className="relative h-full flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl transform transition-all border border-gray-100">
              <ContactTagManager
                contact={editingContact}
                onContactUpdate={handleContactUpdate}
                onClose={() => {
                  setShowTagManager(false);
                  setEditingContact(null);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
