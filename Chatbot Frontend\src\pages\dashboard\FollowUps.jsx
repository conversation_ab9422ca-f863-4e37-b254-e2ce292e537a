import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { api } from "../../services/api";
import Icon from "../../components/ui/icon";
import { Card, CardContent, CardHeader, CardTitle } from "../../components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select2";

/**
 * User-facing follow-up management page
 * Allows users to manage their own AI tagging and follow-up settings
 */
export default function FollowUps() {
  const { user } = useAuth();
  const { showError, showSuccess } = useAlertMigration();

  // State
  const [loading, setLoading] = useState(true);
  const [followUpRules, setFollowUpRules] = useState([]);

  const [analytics, setAnalytics] = useState({
    totalScheduled: 0,
    executed: 0,
    pending: 0,
    executionRate: 0
  });
  const [editingRule, setEditingRule] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    ruleName: '',
    intentCategory: 'interested',
    delayHours: 4,
    maxFollowUps: 1,
    messageTemplate: '',
    isActive: true
  });
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState(null);

  const getUserId = () => {
    const userId = user?.user?.id || user?.profile?.id || user?.id;
    console.log("User ID for follow-ups:", userId);
    return userId;
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchFollowUpRules(),
        fetchAnalytics()
      ]);
    } catch (error) {
      console.error("Error fetching follow-up data:", error);
      showError("Failed to load follow-up data");
    } finally {
      setLoading(false);
    }
  };

  const fetchFollowUpRules = async () => {
    try {
      const response = await api.get(`/api/ai/follow-up-rules?authId=${getUserId()}`);
      const data = response.data;
      console.log("Follow-up rules response:", data);
      if (data.success) {
        setFollowUpRules(data.data || []);
      } else {
        console.error("Failed to fetch follow-up rules:", data.error);
        setFollowUpRules([]);
      }
    } catch (error) {
      console.error("Error fetching follow-up rules:", error);
      setFollowUpRules([]);
    }
  };



  const fetchAnalytics = async () => {
    try {
      // Calculate analytics from recent follow-ups
      const authId = getUserId();
      const response = await api.get(`/api/ai/follow-ups/user-analytics?authId=${authId}`);
      const data = response.data;
      console.log("Analytics response:", data);
      if (data.success) {
        setAnalytics(data.data);
      } else {
        console.error("Failed to fetch analytics:", data.error);
        // Set default analytics if none exist
        setAnalytics({
          totalScheduled: 0,
          executed: 0,
          pending: 0,
          executionRate: 0
        });
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setAnalytics({
        totalScheduled: 0,
        executed: 0,
        pending: 0,
        executionRate: 0
      });
    }
  };



  const handleDeleteRule = async (ruleId) => {
    if (!confirm('Are you sure you want to delete this follow-up rule?')) {
      return;
    }

    try {
      const response = await api.delete(`/api/ai/follow-up-rules/${ruleId}`, {
        data: { authId: getUserId() }
      });

      const data = response.data;
      if (data.success) {
        showSuccess('Follow-up rule deleted successfully!');
        await fetchFollowUpRules();
      } else {
        showError(data.error || 'Failed to delete follow-up rule');
      }
    } catch (error) {
      console.error("Error deleting follow-up rule:", error);
      showError('Failed to delete follow-up rule');
    }
  };

  const createDefaultRules = async () => {
    try {
      const defaultRules = [
        {
          authId: getUserId(),
          ruleName: 'Interested Customer Follow-up',
          intentCategory: 'interested',
          delayHours: 4, // 4 hours - well within 24h window
          maxFollowUps: 1,
          messageTemplate: 'Hi {name}! 👋 Just following up on our conversation. Do you have any other questions about our products/services? I\'m here to help! 😊',
          isActive: true
        },
        {
          authId: getUserId(),
          ruleName: 'Booking Reminder',
          intentCategory: 'booking',
          delayHours: 2, // 2 hours - urgent for bookings
          maxFollowUps: 2,
          messageTemplate: 'Hi {name}! 📅 Following up on your booking inquiry. Would you like to proceed with scheduling? Let me know what works best for you!',
          isActive: true
        },
        {
          authId: getUserId(),
          ruleName: 'Order Follow-up',
          intentCategory: 'placed_order',
          delayHours: 1, // 1 hour - quick confirmation
          maxFollowUps: 1,
          messageTemplate: 'Hi {name}! 📦 Thank you for your order! Is there anything else I can help you with? We appreciate your business! 🙏',
          isActive: false
        }
      ];

      for (const rule of defaultRules) {
        try {
          const response = await api.post('/api/ai/follow-up-rules', rule);
          if (!response.data.success) {
            console.error("Error creating rule:", response.data);
          }
        } catch (error) {
          console.error("Error creating rule:", error);
        }
      }

      showSuccess("Default follow-up rules created successfully!");
      fetchData(); // Refresh all data
    } catch (error) {
      console.error("Error creating default rules:", error);
      showError("Failed to create default rules");
    }
  };

  const openEditModal = (rule = null) => {
    if (rule) {
      // Editing existing rule
      setEditingRule(rule);
      setFormData({
        ruleName: rule.rule_name,
        intentCategory: rule.intent_category,
        delayHours: rule.delay_hours,
        maxFollowUps: rule.max_follow_ups,
        messageTemplate: rule.message_template,
        isActive: rule.is_active
      });
    } else {
      // Creating new rule
      setEditingRule(null);
      setFormData({
        ruleName: '',
        intentCategory: 'interested',
        delayHours: 4,
        maxFollowUps: 1,
        messageTemplate: 'Hi {name}! 👋 Just following up on our conversation. Do you have any other questions about our products/services? I\'m here to help! 😊',
        isActive: true
      });
    }
    setShowEditModal(true);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const closeEditModal = () => {
    setShowEditModal(false);
    setEditingRule(null);
    setSaving(false);
    // Reset form to default values
    setFormData({
      ruleName: '',
      intentCategory: 'interested',
      delayHours: 4,
      maxFollowUps: 1,
      messageTemplate: '',
      isActive: true
    });
  };

  const handleSaveRule = async () => {
    if (saving) return; // Prevent double submission

    try {
      setSaving(true);

      // Validate form
      if (!formData.ruleName.trim()) {
        showError('Rule name is required');
        return;
      }
      if (!formData.messageTemplate.trim()) {
        showError('Message template is required');
        return;
      }

      const ruleData = {
        authId: getUserId(),
        ruleName: formData.ruleName.trim(),
        intentCategory: formData.intentCategory,
        delayHours: parseInt(formData.delayHours),
        maxFollowUps: parseInt(formData.maxFollowUps),
        messageTemplate: formData.messageTemplate.trim(),
        isActive: formData.isActive
      };

      // If editing, include the rule ID for upsert
      if (editingRule) {
        ruleData.id = editingRule.id;
      }

      const response = await api.post('/api/ai/follow-up-rules', ruleData);

      const data = response.data;
      if (data.success) {
        showSuccess(editingRule ? 'Rule updated successfully!' : 'Rule created successfully!');
        closeEditModal();
        fetchFollowUpRules(); // Refresh the rules list
      } else {
        showError(data.error || 'Failed to save rule');
      }
    } catch (error) {
      console.error("Error saving rule:", error);
      showError('Failed to save rule');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteClick = (rule) => {
    setRuleToDelete(rule);
    setShowDeleteDialog(true);
  };
  const handleDeleteConfirm = async () => {
    if (!ruleToDelete) return;
    await handleDeleteRule(ruleToDelete.id);
    setShowDeleteDialog(false);
    setRuleToDelete(null);
  };

  const getIntentCategoryLabel = (category) => {
    const labels = {
      interested: 'Interested Customers',
      booking: 'Booking Inquiries',
      not_interested: 'Not Interested',
      placed_order: 'Placed Orders',
      no_follow_up: 'No Follow-up'
    };
    return labels[category] || category;
  };

  if (loading) {
    return (
      <div className="h-[calc(90vh-6rem)] flex flex-col items-center justify-center">
        <Icon name="Loader2" className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading follow-up settings...</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex justify-between items-start flex-shrink-0">
        <div className="flex-1 max-w-[50%]">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Follow-up Management</h2>
          <p className="text-gray-600">Manage AI auto-tagging and automated follow-up settings for your contacts.</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button onClick={createDefaultRules} className="btn-secondary flex items-center gap-2 px-4 py-2">
            <Icon name="Zap" className="h-4 w-4" />
            Create Defaults
          </button>
          <button onClick={() => openEditModal()} className="btn-primary flex items-center gap-2 px-4 py-2">
            <Icon name="Plus" className="h-4 w-4" />
            Add Rule
          </button>
        </div>
      </div>

      {/* Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-white shadow-md border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Scheduled</p>
                <p className="text-2xl font-bold text-gray-900">{analytics.totalScheduled}</p>
              </div>
              <Icon name="Calendar" className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-md border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Executed</p>
                <p className="text-2xl font-bold text-green-600">{analytics.executed}</p>
              </div>
              <Icon name="CheckCircle" className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-md border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">{analytics.pending}</p>
              </div>
              <Icon name="Clock" className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-md border-none">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-blue-600">{analytics.executionRate}%</p>
              </div>
              <Icon name="TrendingUp" className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Follow-up Rules */}
      <div className="card flex-shrink-0 bg-white shadow-md">
        <div className="flex items-center gap-2 mb-3">
          <Icon name="Settings" className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Follow-up Rules</h3>
        </div>
        {followUpRules.length === 0 ? (
          <div className="text-center py-8">
            <Icon name="MessageCircle" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Follow-up Rules</h3>
            <p className="text-gray-600 mb-4">Create rules to automatically follow up with customers based on their behavior</p>
            <div className="flex gap-3 justify-center">
              <button onClick={createDefaultRules} className="btn-primary">Create Default Rules</button>
              <button onClick={() => openEditModal()} className="btn-secondary">Create Custom Rule</button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {followUpRules.map((rule) => (
              <div key={rule.id} className="bg-white border border-gray-200 rounded-xl p-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4 shadow-sm hover:shadow-md transition-all">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-gray-900 text-lg truncate">{rule.rule_name}</h3>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${rule.is_active ? 'bg-green-50 text-green-700 border border-green-200' : 'bg-gray-50 text-gray-700 border border-gray-200'}`}>{rule.is_active ? 'Active' : 'Inactive'}</span>
                  </div>
                  <div className="flex flex-wrap gap-4 mb-2 text-sm text-gray-600">
                    <div><strong>For:</strong> {getIntentCategoryLabel(rule.intent_category)}</div>
                    <div><strong>Delay:</strong> {rule.delay_hours} hours</div>
                    <div><strong>Max attempts:</strong> {rule.max_follow_ups}</div>
                  </div>
                  <div className="text-sm text-gray-600 truncate"><strong>Message:</strong> {rule.message_template.substring(0, 120)}{rule.message_template.length > 120 && '...'}</div>
                </div>
                <div className="flex items-center gap-2 md:ml-4 mt-2 md:mt-0">
                  <button onClick={() => openEditModal(rule)} className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="Edit rule">
                    <Icon name="Edit" className="w-4 h-4" />
                  </button>
                  <button onClick={() => handleDeleteClick(rule)} className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors" title="Delete rule">
                    <Icon name="Trash2" className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Edit Rule Modal */}
      {showEditModal && (
        <div className="fixed inset-0 z-50 min-h-screen">
          {/* Backdrop */}
          <div className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity" onClick={closeEditModal} />
          {/* Modal */}
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4" onClick={e => e.stopPropagation()}>
            <div className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-xl transform transition-all">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Icon name="Settings" className="h-5 w-5" />
                  {editingRule ? 'Edit Follow-up Rule' : 'Create Follow-up Rule'}
                </h3>
                <button onClick={closeEditModal} className="text-gray-400 hover:text-gray-600"><Icon name="X" className="h-5 w-5" /></button>
              </div>
              <form className="space-y-4" onSubmit={e => { e.preventDefault(); handleSaveRule(); }}>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Rule Name</label>
                  <input type="text" value={formData.ruleName} onChange={e => handleFormChange('ruleName', e.target.value)} className="input-field" placeholder="e.g., Interested Customer Follow-up" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Intent Category</label>
                  <Select value={formData.intentCategory} onValueChange={v => handleFormChange('intentCategory', v)}>
                    <SelectTrigger className="input-field">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="interested">Interested Customers</SelectItem>
                      <SelectItem value="booking">Booking Inquiries</SelectItem>
                      <SelectItem value="placed_order">Order Confirmations</SelectItem>
                      <SelectItem value="not_interested">Not Interested</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Delay (Hours)</label>
                    <input type="number" min="1" max="23" value={formData.delayHours} onChange={e => handleFormChange('delayHours', e.target.value)} className="input-field" />
                    <p className="text-xs text-gray-500 mt-1">Max 23 hours (WhatsApp limit)</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Max Follow-ups</label>
                    <input type="number" min="1" max="5" value={formData.maxFollowUps} onChange={e => handleFormChange('maxFollowUps', e.target.value)} className="input-field" />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Message Template</label>
                  <textarea value={formData.messageTemplate} onChange={e => handleFormChange('messageTemplate', e.target.value)} rows={4} className="input-field resize-none" placeholder="Hi {name}! Just following up on our conversation..." />
                  <p className="text-xs text-gray-500 mt-1">Use {'{name}'} to insert the customer's name</p>
                </div>
                <div className="flex items-center gap-2">
                  <input type="checkbox" id="isActive" checked={formData.isActive} onChange={e => handleFormChange('isActive', e.target.checked)} className="sr-only" />
                  <label htmlFor="isActive" className={`flex items-center justify-center w-5 h-5 rounded border-2 transition-all duration-200 cursor-pointer ${formData.isActive ? 'bg-blue-600 border-blue-600' : 'bg-white border-gray-300 hover:border-blue-400'}`}>{formData.isActive && (<Icon name="Check" className="w-3 h-3 text-white" />)}</label>
                  <label htmlFor="isActive" className="text-sm text-gray-700 cursor-pointer">Rule is active</label>
                </div>
                <div className="flex gap-3 mt-6">
                  <button type="button" onClick={closeEditModal} className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed">Cancel</button>
                  <button type="submit" disabled={saving} className="flex-1 bg-blue-600 text-white px-4 py-2.5 rounded-xl hover:bg-blue-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2">
                    {saving ? (<><Icon name="Loader2" className="w-4 h-4 animate-spin" />Saving...</>) : (editingRule ? 'Update Rule' : 'Create Rule')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      {/* Delete Confirmation Modal */}
      {showDeleteDialog && ruleToDelete && (
        <div className="fixed inset-0 z-50 min-h-screen">
          <div className="absolute inset-0 w-full h-full min-h-screen bg-black/20 backdrop-blur-sm transition-opacity" onClick={() => setShowDeleteDialog(false)} />
          <div className="relative w-full h-full min-h-screen flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl p-6 max-w-md w-full shadow-xl transform transition-all">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
              <p className="text-gray-600 mb-6">Are you sure you want to delete <strong>"{ruleToDelete.rule_name}"</strong>? This action cannot be undone.</p>
              <div className="flex gap-3">
                <button onClick={handleDeleteConfirm} className="flex-1 bg-red-600 text-white px-4 py-2.5 rounded-xl hover:bg-red-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed flex items-center justify-center gap-2">
                  <Icon name="Trash2" className="h-4 w-4" />
                  Delete Rule
                </button>
                <button onClick={() => setShowDeleteDialog(false)} className="flex-1 bg-gray-100 text-gray-700 px-4 py-2.5 rounded-xl hover:bg-gray-200 transition-colors disabled:opacity-60 disabled:cursor-not-allowed">Cancel</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
