import { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { aiService } from "../../services/ai";
import { useNavigate } from "react-router-dom";
import { useAlertMigration } from "../../hooks/useAlertMigration";
import { PageSkeleton } from "../../components/ui/skeleton";
import { useDelayedLoading } from "../../hooks/useDelayedLoading";
import Icon from "../../components/ui/icon";
import BusinessTemplateSelector from "../../components/BusinessTemplateSelector";
import QuickSetupWizard from "../../components/QuickSetupWizard";

export default function ChatbotConfig() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const [settings, setSettings] = useState({
    system_prompt: "",
    order_system_prompt: "",
    model: "gpt-4o-mini",
    temperature: 0.5,
    max_tokens: 200,
    chat_history_limit: 10,
    similarity_threshold: 0.3,
    match_count: 5,
    order_processing_enabled: false,
  });

  // New state for chatbot mode
  const [chatbotMode, setChatbotMode] = useState('default'); // 'default' or 'order'

  const [loading, setLoading] = useState(true);
  const showLoadingState = useDelayedLoading(loading, 200);
  const [saving, setSaving] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [showQuickSetup, setShowQuickSetup] = useState(false);
  const { showError, showSuccess } = useAlertMigration();

  const models = [
    {
      value: "gpt-4o-mini",
      label: "GPT-4o Mini",
      description: "Faster and more cost-effective",
      planRequired: null, // Available to all plans
    },
    {
      value: "gpt-4o",
      label: "GPT-4o (Premium)",
      description: "Latest and most capable model",
      planRequired: "popular", // Requires popular plan or higher
    },
    {
      value: "gpt-3.5-turbo",
      label: "GPT-3.5 Turbo (Premium)",
      description: "Fast and economical",
      planRequired: "popular", // Requires popular plan or higher
    },
  ];

  const defaultSystemPrompt = `You are a friendly customer service assistant. Be helpful, warm, and natural in your responses.
PERSONALITY: Casual and friendly, like talking to a helpful friend. Use Malaysian expressions naturally.
LANGUAGE: Respond in the same language the customer uses (English, Malay, Chinese, or mixed).
KNOWLEDGE: Use only the information provided in your knowledge base. If you don't know something, politely say you're not sure.
ORDERS: You cannot process orders or payments. Direct customers to contact the business directly for purchases.
STYLE: Give complete, helpful information. Be natural and conversational, not robotic.`;

  const defaultOrderSystemPrompt = `You are a friendly sales assistant who helps customers with orders and questions.
PERSONALITY: Warm, helpful, and efficient. Use natural Malaysian expressions.
LANGUAGE: Respond in the customer's preferred language (English, Malay, Chinese, or mixed).
ORDERS: Help customers by showing products, collecting order details step by step, and confirming everything before finalizing.
QUESTIONS: Answer using your knowledge base. Be complete and helpful in your responses.
COMPLETION: When an order is ready, summarize everything clearly and mark it as "ORDER_COMPLETE:" followed by the order summary.`;

  const isModelAvailable = (model) => {
    if (!model.planRequired) return true;
    const userPlan = user?.profile?.plan || "free";
    return userPlan === "popular" || userPlan === "besar";
  };

  const isOrderProcessingAvailable = () => {
    const userPlan = user?.profile?.plan || "free";
    return (
      userPlan === "popular" || userPlan === "besar" || userPlan === "admin"
    );
  };

  const fetchSettings = async () => {
    const userId = user?.user?.id || user?.profile?.id || user?.id;
    if (!userId) return;

    try {
      const data = await aiService.getChatbotSettings(userId);

      if (data.success) {
        const currentModel = data.data.model || "gpt-4o-mini";
        // If user's current model is not available for their plan, default to gpt-4o-mini
        const modelAvailable = models.find(
          (m) => m.value === currentModel && isModelAvailable(m),
        );

        const orderProcessingEnabled = data.data.order_processing_enabled || false;

        setSettings({
          system_prompt: data.data.system_prompt || defaultSystemPrompt,
          order_system_prompt:
            data.data.order_system_prompt || defaultOrderSystemPrompt,
          model: modelAvailable ? currentModel : "gpt-4o-mini",
          temperature: data.data.temperature || 0.5,
          max_tokens: data.data.max_tokens || 200,
          chat_history_limit: data.data.chat_history_limit || 10,
          similarity_threshold: data.data.similarity_threshold || 0.3,
          match_count: data.data.match_count || 5,
          order_processing_enabled: orderProcessingEnabled,
        });

        // Set initial chatbot mode based on order processing setting
        setChatbotMode(orderProcessingEnabled ? 'order' : 'default');
      } else {
        showError(data.error);
      }
    } catch {
      showError("Failed to fetch chatbot settings");
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    const userId = user?.user?.id || user?.profile?.id || user?.id;
    if (!userId) return;

    setSaving(true);

    try {
      // Determine if order processing should be enabled based on mode
      const orderProcessingEnabled = chatbotMode === 'order' && isOrderProcessingAvailable();

      const data = await aiService.updateChatbotSettings({
        authId: userId,
        systemPrompt: settings.system_prompt,
        orderSystemPrompt: settings.order_system_prompt,
        model: settings.model,
        temperature: settings.temperature,
        maxTokens: settings.max_tokens,
        chatHistoryLimit: settings.chat_history_limit,
        similarityThreshold: settings.similarity_threshold,
        matchCount: settings.match_count,
        orderProcessingEnabled: orderProcessingEnabled,
      });

      if (data.success) {
        // Update local settings to reflect the saved state
        setSettings(prev => ({
          ...prev,
          order_processing_enabled: orderProcessingEnabled
        }));
        showSuccess("Settings saved successfully!");
      } else {
        showError(data.error);
      }
    } catch (error) {
      showError(error.message || "Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      system_prompt: defaultSystemPrompt,
      order_system_prompt: defaultOrderSystemPrompt,
      model: "gpt-4o-mini",
      temperature: 0.5,
      max_tokens: 200,
      chat_history_limit: 10,
      similarity_threshold: 0.3,
      match_count: 5,
      order_processing_enabled: false,
    });
    setChatbotMode('default'); // Reset to default mode
  };

  const applyBusinessTemplate = (template) => {
    setSettings({
      system_prompt: template.systemPrompt,
      order_system_prompt: template.orderPrompt,
      model: "gpt-4o-mini", // Keep current model selection
      temperature: template.settings.temperature,
      max_tokens: template.settings.max_tokens,
      chat_history_limit: template.settings.chat_history_limit,
      similarity_threshold: template.settings.similarity_threshold,
      match_count: template.settings.match_count,
      order_processing_enabled: false, // Keep current order processing state
    });
    setChatbotMode('default'); // Reset to default mode when applying template
    showSuccess(`Applied ${template.name} template successfully! Don't forget to save your settings.`);
  };

  const handleQuickSetupComplete = (configuration, wizardData) => {
    setSettings({
      ...settings,
      ...configuration
    });
    showSuccess(`Quick setup completed! Your chatbot is configured for ${wizardData.businessName || 'your business'}.`);
    // Auto-save the configuration
    setTimeout(() => {
      saveSettings();
    }, 500);
  };



  useEffect(() => {
    if (user) {
      fetchSettings();
    }
  }, [user]);

  if (showLoadingState) {
    return <PageSkeleton cardCount={1} />;
  }

  return (
    <div className="space-y-6">
      {/* Header Section - Responsive Layout */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Chatbot Configuration
          </h2>
          <p className="text-gray-600">
            Customize your chatbot's behavior, personality, and performance
            settings.
          </p>
        </div>

        {/* Action Buttons - Responsive Grid */}
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 lg:flex-shrink-0">
          {/* Primary Actions Row */}
          <div className="flex gap-2 sm:gap-3">
            <button
              onClick={() => setShowQuickSetup(true)}
              className="btn-secondary flex items-center justify-center gap-2 flex-1 sm:flex-initial"
              disabled={saving}
            >
              <Icon name="Zap" className="w-4 h-4" />
              <span className="hidden sm:inline">Quick Setup</span>
              <span className="sm:hidden">Setup</span>
            </button>
            <button
              onClick={() => setShowTemplateSelector(true)}
              className="btn-secondary flex items-center justify-center gap-2 flex-1 sm:flex-initial"
              disabled={saving}
            >
              <Icon name="FileTemplate" className="w-4 h-4" />
              <span className="hidden sm:inline">Templates</span>
              <span className="sm:hidden">Templates</span>
            </button>
          </div>

          {/* Secondary Actions Row */}
          <div className="flex gap-2 sm:gap-3">
            <button
              onClick={resetToDefaults}
              className="btn-secondary flex items-center justify-center gap-2 flex-1 sm:flex-initial"
              disabled={saving}
            >
              <Icon name="RotateCcw" className="w-4 h-4" />
              <span>Reset</span>
            </button>
            <button
              onClick={saveSettings}
              className="btn-primary flex items-center justify-center gap-2 px-6 py-2.5 flex-1 sm:flex-initial"
              disabled={saving}
            >
              {saving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Icon name="Save" className="w-4 h-4" />
                  <span>Save</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Error and success messages now handled by global AlertContainer */}

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Chatbot Mode Switcher and Prompt */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Chatbot Configuration
              </h3>
              {chatbotMode === 'order' && !isOrderProcessingAvailable() && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <Icon name="Lock" className="w-3 h-3 mr-1" />
                  Premium Feature
                </span>
              )}
            </div>

            {/* Mode Switcher */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Chatbot Mode
              </label>
              <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="chatbotMode"
                    value="default"
                    checked={chatbotMode === 'default'}
                    onChange={(e) => setChatbotMode(e.target.value)}
                    className="text-blue-600"
                  />
                  <span className="text-sm font-medium text-gray-900">
                    Default Mode
                  </span>
                </label>
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="radio"
                    name="chatbotMode"
                    value="order"
                    checked={chatbotMode === 'order'}
                    onChange={(e) => {
                      if (!isOrderProcessingAvailable()) {
                        showError(
                          "Order processing mode is only available for paid plans. Please upgrade your subscription."
                        );
                        return;
                      }
                      setChatbotMode(e.target.value);
                    }}
                    disabled={!isOrderProcessingAvailable()}
                    className="text-blue-600"
                  />
                  <span className={`text-sm font-medium ${!isOrderProcessingAvailable() ? "text-gray-400" : "text-gray-900"}`}>
                    Order Processing Mode
                  </span>
                </label>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                {chatbotMode === 'default'
                  ? "General customer service and information mode"
                  : "Specialized mode for handling customer orders and transactions"
                }
              </p>
            </div>

            {/* Upgrade Notice for Order Mode */}
            {chatbotMode === 'order' && !isOrderProcessingAvailable() && (
              <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                <div className="flex items-start gap-3">
                  <Icon
                    name="Lock"
                    className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0"
                  />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-900">
                      Upgrade Required for Order Processing Mode
                    </h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Order processing features are available for Kedai Popular
                      plan and above. Upgrade to enable advanced order
                      management capabilities.
                    </p>
                    <button
                      onClick={() => navigate("/pricing")}
                      className="mt-2 text-sm bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
                    >
                      Upgrade Now
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* System Prompt */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {chatbotMode === 'default' ? 'System Prompt' : 'Order System Prompt'}
              </label>
              <p className="text-sm text-gray-600 mb-3">
                {chatbotMode === 'default'
                  ? "Define your chatbot's personality, behavior, and response style. This is the core instruction that guides all interactions."
                  : "Define specific instructions for how your chatbot should handle order processing, collect customer information, and manage orders."
                }
              </p>
              <textarea
                rows="8"
                className="input-field resize-none"
                value={chatbotMode === 'default' ? settings.system_prompt : settings.order_system_prompt}
                onChange={(e) =>
                  setSettings((prev) => ({
                    ...prev,
                    [chatbotMode === 'default' ? 'system_prompt' : 'order_system_prompt']: e.target.value,
                  }))
                }
                placeholder={chatbotMode === 'default' ? defaultSystemPrompt : defaultOrderSystemPrompt}
                disabled={chatbotMode === 'order' && !isOrderProcessingAvailable()}
              />
              <p className="mt-2 text-xs text-gray-500">
                {chatbotMode === 'default'
                  ? "Tip: Be specific about tone, style, and what kind of responses you want. Include guidelines for handling unknown questions."
                  : "Tip: Include instructions for collecting customer details, product selection, quantity confirmation, delivery preferences, and order confirmation steps."
                }
              </p>
            </div>
          </div>
        </div>



        {/* Model Selection */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Model</h3>
          <p className="text-sm text-gray-600 mb-4">
            Choose the AI model that powers your chatbot. Premium models require
            a Popular plan or higher.
          </p>
          <div className="space-y-3">
            {models.map((model) => {
              const available = isModelAvailable(model);
              return (
                <label
                  key={model.value}
                  className={`flex items-start gap-3 p-3 border border-gray-200 rounded-xl transition-all ${
                    available
                      ? "hover:bg-gray-50 cursor-pointer"
                      : "bg-gray-50 border-gray-200 opacity-60 cursor-not-allowed"
                  }`}
                >
                  <input
                    type="radio"
                    name="model"
                    value={model.value}
                    checked={settings.model === model.value}
                    onChange={(e) =>
                      available &&
                      setSettings((prev) => ({
                        ...prev,
                        model: e.target.value,
                      }))
                    }
                    disabled={!available}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p
                        className={`font-medium ${available ? "text-gray-900" : "text-gray-500"}`}
                      >
                        {model.label}
                      </p>
                      {!available && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          <Icon name="Lock" className="w-3 h-3 mr-1" />
                          Premium Model
                        </span>
                      )}
                    </div>
                    <p
                      className={`text-sm ${available ? "text-gray-600" : "text-gray-400"}`}
                    >
                      {model.description}
                    </p>
                    {!available && (
                      <p className="text-xs text-yellow-600 mt-1">
                        Upgrade to Kedai Popular plan or higher to unlock this
                        model
                      </p>
                    )}
                  </div>
                </label>
              );
            })}
          </div>

          {/* Plan Upgrade Notice */}
          {(user?.profile?.plan === "free" ||
            user?.profile?.plan === "kecil" ||
            !user?.profile?.plan) && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-xl">
              <div className="flex items-start gap-3">
                <Icon
                  name="Lock"
                  className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0"
                />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">
                    Unlock Premium Models
                  </h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Upgrade to the Kedai Popular plan or higher to access
                    GPT-4o, GPT-3.5 Turbo, and other advanced models with
                    enhanced capabilities.
                  </p>
                  <button
                    onClick={() => navigate("/pricing")}
                    className="mt-2 text-sm bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Upgrade Now
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Response Settings */}
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Response Settings
          </h3>

          <div className="space-y-6">
            {/* Temperature */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Creativity Level (Temperature): {settings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={settings.temperature}
                onChange={(e) =>
                  setSettings((prev) => ({
                    ...prev,
                    temperature: parseFloat(e.target.value),
                  }))
                }
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Focused (0.0)</span>
                <span>Balanced (0.5)</span>
                <span>Creative (1.0)</span>
              </div>
              <p className="text-xs text-gray-600 mt-2">
                Higher values make responses more creative but less predictable.
              </p>
            </div>

            {/* Max Tokens */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Response Length (tokens)
              </label>
              <input
                type="number"
                min="50"
                max="2000"
                step="50"
                value={settings.max_tokens}
                onChange={(e) =>
                  setSettings((prev) => ({
                    ...prev,
                    max_tokens: parseInt(e.target.value),
                  }))
                }
                className="input-field"
              />
              <p className="text-xs text-gray-600 mt-1">
                Limits response length. ~1 token ≈ 4 characters. Higher values
                cost more.
              </p>
            </div>

            {/* Chat History Limit */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Conversation Memory (messages)
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={settings.chat_history_limit}
                onChange={(e) =>
                  setSettings((prev) => ({
                    ...prev,
                    chat_history_limit: parseInt(e.target.value),
                  }))
                }
                className="input-field"
              />
              <p className="text-xs text-gray-600 mt-1">
                How many previous messages to remember in conversations.
              </p>
            </div>
          </div>
        </div>

        {/* Knowledge Base Settings */}
        <div className="lg:col-span-2">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Knowledge Base Settings
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              Configure how your chatbot searches and uses information from your
              knowledge base.
            </p>

            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Similarity Threshold: {settings.similarity_threshold}
                </label>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.05"
                  value={settings.similarity_threshold}
                  onChange={(e) =>
                    setSettings((prev) => ({
                      ...prev,
                      similarity_threshold: parseFloat(e.target.value),
                    }))
                  }
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Loose (0.1)</span>
                  <span>Balanced (0.5)</span>
                  <span>Strict (1.0)</span>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  How closely content must match user questions to be used.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Knowledge Sections
                </label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={settings.match_count}
                  onChange={(e) =>
                    setSettings((prev) => ({
                      ...prev,
                      match_count: parseInt(e.target.value),
                    }))
                  }
                  className="input-field"
                />
                <p className="text-xs text-gray-600 mt-1">
                  Maximum number of knowledge base sections to include per
                  response.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Setup Wizard */}
      {showQuickSetup && (
        <QuickSetupWizard
          onComplete={handleQuickSetupComplete}
          onClose={() => setShowQuickSetup(false)}
        />
      )}

      {/* Business Template Selector Modal */}
      {showTemplateSelector && (
        <BusinessTemplateSelector
          onTemplateSelect={applyBusinessTemplate}
          onClose={() => setShowTemplateSelector(false)}
        />
      )}
    </div>
  );
}
